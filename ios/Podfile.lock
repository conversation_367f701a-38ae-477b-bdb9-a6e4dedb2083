PODS:
  - Flutter (1.0.0)
  - Google-Mobile-Ads-SDK (12.2.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (6.0.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 12.2.0)
    - webview_flutter_wkwebview
  - GoogleUserMessagingPlatform (3.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Google-Mobile-Ads-SDK
    - GoogleUserMessagingPlatform

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  Google-Mobile-Ads-SDK: 1dfb0c3cb46c7e2b00b0f4de74a1e06d9ea25d67
  google_mobile_ads: cab27248050510554aa646195eacc393ce7406ad
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
