import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_provider.dart';
import '../utils/constants.dart';
import '../utils/fish_image.dart';
import '../widgets/danmaku_manager.dart';
import 'fish_list_screen.dart';
import 'settings_screen.dart';

/// 主界面
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.tapAnimationDuration,
      vsync: this,
    );

    // 初始化游戏状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final gameProvider = Provider.of<GameProvider>(context, listen: false);
      gameProvider.setAnimationController(_animationController);
      gameProvider.init();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppColors.backgroundGradient),
        child: SafeArea(
          child: Consumer<GameProvider>(
            builder: (context, gameProvider, child) {
              if (gameProvider.isLoading) {
                return const Center(
                  child: CircularProgressIndicator(color: AppColors.primary),
                );
              }

              if (gameProvider.errorMessage != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: AppColors.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        gameProvider.errorMessage!,
                        style: const TextStyle(
                          color: AppColors.error,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          gameProvider.clearError();
                          gameProvider.init();
                        },
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                );
              }

              return DanmakuManager(
                enabled: gameProvider.isDanmakuEnabled,
                height:
                    MediaQuery.of(context).size.height * 0.6, // 弹幕区域高度，覆盖屏幕的60%
                child: GestureDetector(
                  onTap: () => _handleTap(gameProvider),
                  child: Column(
                    children: [
                      // 顶部状态栏
                      _buildTopBar(context, gameProvider),

                      // 中央点击区域
                      Expanded(child: _buildTapArea(context, gameProvider)),

                      // 底部按钮
                      _buildBottomButtons(context),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 处理点击事件
  void _handleTap(GameProvider gameProvider) {
    // 调用 GameProvider 的 onTap 方法
    gameProvider
        .onTap()
        .then((_) {
          // 显示弹幕（仅在弹幕开启时）
          if (mounted && gameProvider.isDanmakuEnabled) {
            DanmakuManager.addDanmaku(context, '功德+1');
          }

          // 点击处理完成
        })
        .catchError((error) {
          if (kDebugMode) {
            print('点击处理错误: $error');
          }
        });
  }

  /// 构建顶部状态栏
  Widget _buildTopBar(BuildContext context, GameProvider gameProvider) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // 第一行：木鱼名称和设置按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 当前木鱼名称 - 给更多空间
              Expanded(
                child: Text(
                  gameProvider.currentFishName,
                  style: const TextStyle(
                    fontSize: AppConstants.titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // 设置按钮
              IconButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SettingsScreen(),
                    ),
                  );
                },
                icon: const Icon(
                  Icons.settings,
                  color: AppColors.textPrimary,
                  size: 24,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // 第二行：点击统计
          Row(
            children: [
              // 今日点击次数
              Expanded(
                child: _buildStatCard(
                  '今日',
                  '${gameProvider.todayTaps}',
                  AppColors.primary,
                ),
              ),

              const SizedBox(width: 12),

              // 总点击次数
              Expanded(
                child: _buildStatCard(
                  '总计',
                  '${gameProvider.totalTaps}',
                  AppColors.primaryDark,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建中央点击区域
  Widget _buildTapArea(BuildContext context, GameProvider gameProvider) {
    return Center(
      child: AnimatedBuilder(
        animation:
            gameProvider.scaleAnimation ?? const AlwaysStoppedAnimation(1.0),
        builder: (context, child) {
          return Transform.scale(
            scale: gameProvider.finalScale, // 使用包含回弹效果的最终缩放值
            child: FishImage(
              fishId: gameProvider.userData.currentFishId,
              size: AppConstants.fishImageSize,
              isUnlocked: true,
            ),
          );
        },
      ),
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const FishListScreen()),
            );
          },
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: AppColors.button,
            foregroundColor: AppColors.textPrimary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                AppConstants.defaultBorderRadius,
              ),
            ),
          ),
          child: const Text(
            '木鱼列表',
            style: TextStyle(
              fontSize: AppConstants.buttonFontSize,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard(String label, String value, Color accentColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(color: accentColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: accentColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: accentColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: AppConstants.counterFontSize,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
