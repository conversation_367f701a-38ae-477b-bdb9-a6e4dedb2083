import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_data.dart';
import '../models/wooden_fish.dart';
import '../services/storage_service.dart';
import '../utils/constants.dart';

/// 游戏状态管理Provider
class GameProvider extends ChangeNotifier {
  UserData _userData = UserData();
  List<WoodenFish> _fishList = [];
  bool _isLoading = true;
  String? _errorMessage;

  // 动画控制器相关
  AnimationController? _tapAnimationController;
  Animation<double>? _scaleAnimation;

  // 动画状态管理
  bool _isAnimating = false;

  /// 获取用户数据
  UserData get userData => _userData;

  /// 获取木鱼列表
  List<WoodenFish> get fishList => _fishList;

  /// 获取当前使用的木鱼
  WoodenFish? get currentFish {
    return _fishList.isNotEmpty
        ? _fishList.firstWhere(
            (fish) => fish.id == _userData.currentFishId,
            orElse: () => _fishList.first,
          )
        : null;
  }

  /// 获取今日点击次数
  int get todayTaps => _userData.todayTaps;

  /// 获取总点击次数
  int get totalTaps => _userData.totalTaps;

  /// 获取当前木鱼名称
  String get currentFishName => currentFish?.name ?? '基础木鱼';

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 错误信息
  String? get errorMessage => _errorMessage;

  /// 获取缩放动画
  Animation<double>? get scaleAnimation => _scaleAnimation;

  /// 获取最终的缩放值
  double get finalScale {
    return _scaleAnimation?.value ?? 1.0;
  }

  /// 初始化Provider
  Future<void> init() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // 初始化存储服务
      await StorageService.instance.init();

      // 加载用户数据
      _userData = await StorageService.instance.loadUserData();

      // 检查是否需要每日重置
      if (_userData.shouldResetDaily()) {
        _userData.resetDailyTaps();
        await _saveUserData();
      }

      // 初始化木鱼列表
      _initializeFishList();

      // 检查并解锁新木鱼
      await _checkAndUnlockFish();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = '初始化失败: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 初始化木鱼列表
  void _initializeFishList() {
    _fishList = FishConfig.defaultFishList.map((fish) {
      return fish.copyWith(isUnlocked: _userData.isFishUnlocked(fish.id));
    }).toList();
  }

  /// 设置动画控制器
  void setAnimationController(AnimationController controller) {
    _tapAnimationController = controller;

    // 创建分段动画曲线，为forward和reverse阶段设置不同的曲线
    _scaleAnimation =
        Tween<double>(
          begin: 1.0,
          end: 0.85, // 适当的缩放幅度
        ).animate(
          CurvedAnimation(
            parent: controller,
            curve: Curves.easeOutQuint, // 缩小阶段使用快速的曲线
            reverseCurve: Curves.easeInOutCubic, // 放大阶段使用更平滑的曲线
          ),
        );

    // 监听动画状态变化
    controller.addStatusListener((status) {
      _isAnimating =
          status == AnimationStatus.forward ||
          status == AnimationStatus.reverse;
    });
  }

  /// 处理点击事件
  Future<void> onTap() async {
    try {
      // 立即增加点击次数
      _userData.addTap();

      // 立即通知UI更新，确保浮现文字能及时显示
      notifyListeners();

      // 异步播放点击动画，不阻塞后续点击
      _playTapAnimationAsync();

      // 异步处理其他操作，不阻塞UI
      _processBackgroundTasks();
    } catch (e) {
      _errorMessage = '点击处理失败: $e';
      notifyListeners();
    }
  }

  /// 添加奖励点击次数（用于激励广告）
  Future<void> addBonusTaps(int bonusAmount) async {
    try {
      // 添加奖励点击次数
      for (int i = 0; i < bonusAmount; i++) {
        _userData.addTap();
      }

      // 保存数据
      await _saveUserData();

      // 通知UI更新
      notifyListeners();
    } catch (e) {
      _errorMessage = '添加奖励失败: $e';
      notifyListeners();
    }
  }

  /// 播放点击动画 - 简化版本，避免抖动
  void _playTapAnimationAsync() {
    if (_tapAnimationController == null) return;

    if (_isAnimating) {
      // 如果正在动画中，重新开始动画（提供即时反馈）
      _restartAnimation();
    } else {
      // 如果没有动画，开始新的动画
      _startNewAnimation();
    }
  }

  /// 开始新的动画
  void _startNewAnimation() {
    if (_tapAnimationController == null) return;

    _tapAnimationController!.reset();
    _tapAnimationController!
        .forward()
        .then((_) {
          if (_tapAnimationController != null) {
            return _tapAnimationController!.reverse();
          }
        })
        .catchError((error) {
          if (kDebugMode) {
            print('点击动画错误: $error');
          }
          return null;
        });
  }

  /// 重新开始动画（用于动画过程中的点击）
  void _restartAnimation() {
    if (_tapAnimationController == null) return;

    // 平滑地重新开始动画
    _tapAnimationController!.reset();
    _startNewAnimation();
  }

  /// 异步处理后台任务
  Future<void> _processBackgroundTasks() async {
    try {
      // 检查并解锁新木鱼
      await _checkAndUnlockFish();

      // 保存数据
      await _saveUserData();

      // 再次通知更新（如果有新解锁的内容）
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('后台任务处理错误: $e');
      }
      // 后台任务失败不影响主要功能
    }
  }

  /// 检查并解锁新木鱼
  Future<void> _checkAndUnlockFish() async {
    final unlockableFish = FishConfig.getUnlockableFish(
      _userData.totalTaps,
      _userData.unlockedFish,
    );

    for (String fishId in unlockableFish) {
      _userData.unlockFish(fishId);

      // 更新木鱼列表中的解锁状态
      final fishIndex = _fishList.indexWhere((fish) => fish.id == fishId);
      if (fishIndex != -1) {
        _fishList[fishIndex] = _fishList[fishIndex].copyWith(isUnlocked: true);
      }

      // 可以在这里添加解锁提示逻辑
      if (kDebugMode) {
        print('解锁新木鱼: ${FishConfig.getFishById(fishId)?.name}');
      }
    }
  }

  /// 切换木鱼
  Future<void> switchFish(String fishId) async {
    try {
      if (_userData.isFishUnlocked(fishId)) {
        _userData.switchFish(fishId);
        await _saveUserData();
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = '切换木鱼失败: $e';
      notifyListeners();
    }
  }

  /// 获取已解锁的木鱼列表
  List<WoodenFish> getUnlockedFishList() {
    return _fishList.where((fish) => fish.isUnlocked).toList();
  }

  /// 获取木鱼解锁进度
  double getFishUnlockProgress(String fishId) {
    final fish = FishConfig.getFishById(fishId);
    if (fish == null) return 0.0;

    if (_userData.isFishUnlocked(fishId)) return 1.0;

    if (fish.unlockThreshold == 0) return 1.0;

    return (_userData.totalTaps / fish.unlockThreshold).clamp(0.0, 1.0);
  }

  /// 获取弹幕开关状态
  bool get isDanmakuEnabled => _userData.danmakuEnabled;

  /// 切换弹幕开关
  Future<void> toggleDanmaku() async {
    _userData.toggleDanmaku();
    await _saveUserData();
    notifyListeners();
  }

  /// 保存用户数据
  Future<void> _saveUserData() async {
    try {
      await StorageService.instance.saveUserData(_userData);
    } catch (e) {
      if (kDebugMode) {
        print('保存用户数据失败: $e');
      }
    }
  }

  /// 重置游戏数据
  Future<void> resetGameData() async {
    try {
      await StorageService.instance.deleteUserData();
      _userData = UserData();
      _initializeFishList();
      await _saveUserData();
      notifyListeners();
    } catch (e) {
      _errorMessage = '重置数据失败: $e';
      notifyListeners();
    }
  }

  /// 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    // 不要在这里dispose AnimationController，因为它是由外部创建和管理的
    super.dispose();
  }
}
