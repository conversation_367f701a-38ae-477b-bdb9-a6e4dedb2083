import 'package:flutter/material.dart';

/// 弹幕模型
class Danmaku {
  final String id;
  final String text;
  final Color color;
  final double fontSize;
  final double speed;
  final DateTime createdAt;
  
  Danmaku({
    required this.id,
    required this.text,
    this.color = const Color(0xFF4CAF50),
    this.fontSize = 16.0,
    this.speed = 100.0, // 像素/秒
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  /// 复制并修改属性
  Danmaku copyWith({
    String? id,
    String? text,
    Color? color,
    double? fontSize,
    double? speed,
    DateTime? createdAt,
  }) {
    return Danmaku(
      id: id ?? this.id,
      text: text ?? this.text,
      color: color ?? this.color,
      fontSize: fontSize ?? this.fontSize,
      speed: speed ?? this.speed,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Danmaku(id: $id, text: $text, color: $color, fontSize: $fontSize, speed: $speed, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Danmaku && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
