import 'dart:math';
import 'package:flutter/material.dart';
import '../models/danmaku.dart';

/// 弹幕管理器
class DanmakuManager extends StatefulWidget {
  final Widget child;
  final bool enabled;
  final double height; // 弹幕区域高度

  const DanmakuManager({
    super.key,
    required this.child,
    this.enabled = true,
    this.height = 200.0,
  });

  @override
  State<DanmakuManager> createState() => _DanmakuManagerState();

  /// 全局实例，用于访问弹幕管理器
  static _DanmakuManagerState? _instance;

  /// 添加弹幕的静态方法
  static void addDanmaku(BuildContext context, String text, {Color? color}) {
    if (_instance != null) {
      _instance!.addDanmaku(text, color: color);
    }
  }
}

class _DanmakuManagerState extends State<DanmakuManager>
    with TickerProviderStateMixin {
  final List<_DanmakuItem> _danmakuItems = [];
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    // 注册全局实例
    DanmakuManager._instance = this;
  }

  @override
  void dispose() {
    // 注销全局实例
    DanmakuManager._instance = null;

    // 清理所有动画控制器
    for (var item in _danmakuItems) {
      item.controller.dispose();
    }
    super.dispose();
  }

  /// 添加弹幕
  void addDanmaku(String text, {Color? color}) {
    if (!widget.enabled || !mounted) return;

    final danmaku = Danmaku(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: text,
      color: color ?? const Color(0xFFFFD700), // 使用金色，更明显
      fontSize: 20.0, // 更大的字体
      speed: 80.0 + _random.nextDouble() * 40.0, // 80-120像素/秒的随机速度
    );

    final controller = AnimationController(
      duration: const Duration(seconds: 4), // 4秒穿越屏幕，稍快一些
      vsync: this,
    );

    final item = _DanmakuItem(
      danmaku: danmaku,
      controller: controller,
      yPosition: _getRandomYPosition(),
    );

    setState(() {
      _danmakuItems.add(item);
    });

    // 开始动画
    controller.forward().then((_) {
      // 动画完成后移除弹幕
      if (mounted) {
        setState(() {
          _danmakuItems.remove(item);
        });
      }
      controller.dispose();
    });
  }

  /// 获取随机Y位置
  double _getRandomYPosition() {
    // 在弹幕区域内随机分布，确保有足够的边距
    final minY = 100.0; // 从顶部100px开始，避开状态栏
    final maxY = widget.height - 100; // 减去底部边距
    return minY + _random.nextDouble() * (maxY - minY);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.enabled)
          Positioned.fill(
            child: IgnorePointer(
              child: Stack(
                clipBehavior: Clip.none, // 允许弹幕超出边界
                children: _danmakuItems.map((item) {
                  return AnimatedBuilder(
                    animation: item.controller,
                    builder: (context, child) {
                      final screenWidth = MediaQuery.of(context).size.width;
                      final progress = item.controller.value;

                      // 从右边屏幕外开始，移动到左边屏幕外
                      // 修复定位逻辑：从右侧100px外开始，移动到左侧-200px
                      final startX = screenWidth + 100;
                      final endX = -200.0;
                      final x = startX + (endX - startX) * progress;

                      return Positioned(
                        left: x,
                        top: item.yPosition,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: item.danmaku.color.withValues(alpha: 0.5),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            item.danmaku.text,
                            style: TextStyle(
                              color: item.danmaku.color,
                              fontSize: item.danmaku.fontSize,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: const Offset(1, 1),
                                  blurRadius: 3,
                                  color: Colors.black.withValues(alpha: 0.8),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  );
                }).toList(),
              ),
            ),
          ),
      ],
    );
  }
}

/// 弹幕项目
class _DanmakuItem {
  final Danmaku danmaku;
  final AnimationController controller;
  final double yPosition;

  _DanmakuItem({
    required this.danmaku,
    required this.controller,
    required this.yPosition,
  });
}
