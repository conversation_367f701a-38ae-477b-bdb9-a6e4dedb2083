import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

/// 广告服务类，管理所有广告相关功能
class AdService {
  static final AdService _instance = AdService._internal();
  factory AdService() => _instance;
  AdService._internal();

  // 广告单元ID（测试用）
  static String get _bannerAdUnitId {
    if (kDebugMode) {
      return Platform.isAndroid
          ? 'ca-app-pub-7280036561880717/3940465534' // Android测试横幅广告ID
          : 'ca-app-pub-7280036561880717/1418243977'; // iOS测试横幅广告ID
    } else {
      return Platform.isAndroid
          ? 'ca-app-pub-7280036561880717~7970359828' // 生产环境需要替换为真实ID
          : 'ca-app-pub-7280036561880717~9091869803'; // 生产环境需要替换为真实ID
    }
  }

  // 广告实例
  BannerAd? _bannerAd;

  // 广告状态
  bool _isBannerAdLoaded = false;

  /// 初始化AdMob
  Future<void> initialize() async {
    try {
      await MobileAds.instance.initialize();
      if (kDebugMode) {
        print('AdMob初始化成功');
      }

      // 预加载横幅广告
      _loadBannerAd();
    } catch (e) {
      if (kDebugMode) {
        print('AdMob初始化失败: $e');
      }
    }
  }

  /// 加载横幅广告
  void _loadBannerAd() {
    _bannerAd = BannerAd(
      adUnitId: _bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          _isBannerAdLoaded = true;
          if (kDebugMode) {
            print('横幅广告加载成功');
          }
        },
        onAdFailedToLoad: (ad, error) {
          _isBannerAdLoaded = false;
          ad.dispose();
          if (kDebugMode) {
            print('横幅广告加载失败: $error');
          }
        },
      ),
    );
    _bannerAd?.load();
  }

  /// 获取横幅广告
  BannerAd? get bannerAd => _isBannerAdLoaded ? _bannerAd : null;

  /// 释放资源
  void dispose() {
    _bannerAd?.dispose();
  }
}
